import * as vscode from 'vscode';

import { ServiceRegistry } from '../core/ServiceRegistry';
import { Logger } from '../utils/Logger';

import { ConfigurationService } from './ConfigurationService';
import { FileOperationsService } from './FileOperationsService';
import { ProjectDetectionService } from './ProjectDetectionService';
import { WorkspaceAnalysisService } from './WorkspaceAnalysisService';

/**
 * Command service for handling VS Code commands
 * Provides centralized command handling for UIOrbit extension
 */
export class CommandService {
  constructor(private serviceRegistry: ServiceRegistry) {}

  /**
   * Open the UIOrbit chat interface
   */
  async openChat(): Promise<void> {
    try {
      Logger.info('Opening UIOrbit chat...');

      // Focus on the chat view
      await vscode.commands.executeCommand('uiorbit.chatView.focus');

      // Show information message
      vscode.window.showInformationMessage('UIOrbit chat is now open. Start chatting with your AI frontend assistant!');

    } catch (error) {
      Logger.error('Failed to open chat:', error);
      vscode.window.showErrorMessage('Failed to open UIOrbit chat. Please try again.');
    }
  }

  /**
   * Generate a component in the selected folder
   */
  async generateComponent(uri?: vscode.Uri): Promise<void> {
    try {
      Logger.info('Generating component...');

      // Get the target folder
      const targetFolder = uri || await this.selectTargetFolder();
      if (!targetFolder) {
        return;
      }

      // Check if API key is configured
      const configService = this.serviceRegistry.get<ConfigurationService>('configuration');
      if (!configService?.hasOpenAIApiKey()) {
        vscode.window.showWarningMessage(
          'OpenAI API key not configured. Please set your API key in settings or .env file.',
          'Open Settings'
        ).then(selection => {
          if (selection === 'Open Settings') {
            vscode.commands.executeCommand('workbench.action.openSettings', 'uiorbit.openaiApiKey');
          }
        });
        return;
      }

      // Get component details from user
      const componentName = await vscode.window.showInputBox({
        prompt: 'Enter component name',
        placeHolder: 'e.g., Button, Card, Modal',
        validateInput: (value) => {
          if (!value || value.trim().length === 0) {
            return 'Component name is required';
          }
          if (!/^[A-Z][a-zA-Z0-9]*$/.test(value.trim())) {
            return 'Component name must start with uppercase letter and contain only letters and numbers';
          }
          return null;
        }
      });

      if (!componentName) {
        return;
      }

      const componentDescription = await vscode.window.showInputBox({
        prompt: 'Describe the component (optional)',
        placeHolder: 'e.g., A reusable button with multiple variants and accessibility features'
      });

      // Show progress
      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: `Generating ${componentName} component...`,
        cancellable: false
      }, async (progress) => {
        progress.report({ increment: 0, message: 'Analyzing project structure...' });
        
        // TODO: Implement actual component generation
        // This will be implemented in Phase 1, Week 4
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        progress.report({ increment: 50, message: 'Generating component code...' });
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        progress.report({ increment: 100, message: 'Component generated successfully!' });
      });

      vscode.window.showInformationMessage(
        `Component ${componentName} will be generated here. (Implementation coming in Phase 1, Week 4)`
      );

    } catch (error) {
      Logger.error('Failed to generate component:', error);
      vscode.window.showErrorMessage('Failed to generate component. Please try again.');
    }
  }

  /**
   * Analyze the selected code
   */
  async analyzeCode(): Promise<void> {
    try {
      Logger.info('Analyzing code...');

      const editor = vscode.window.activeTextEditor;
      if (!editor) {
        vscode.window.showWarningMessage('No active editor found. Please open a file and select some code.');
        return;
      }

      const selection = editor.selection;
      const selectedText = editor.document.getText(selection);

      if (!selectedText || selectedText.trim().length === 0) {
        vscode.window.showWarningMessage('No code selected. Please select some code to analyze.');
        return;
      }

      // Check if API key is configured
      const configService = this.serviceRegistry.get<ConfigurationService>('configuration');
      if (!configService?.hasOpenAIApiKey()) {
        vscode.window.showWarningMessage(
          'OpenAI API key not configured. Please set your API key in settings or .env file.',
          'Open Settings'
        ).then(selection => {
          if (selection === 'Open Settings') {
            vscode.commands.executeCommand('workbench.action.openSettings', 'uiorbit.openaiApiKey');
          }
        });
        return;
      }

      // Show progress
      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: 'Analyzing selected code...',
        cancellable: false
      }, async (progress) => {
        progress.report({ increment: 0, message: 'Processing code...' });
        
        // TODO: Implement actual code analysis
        // This will be implemented in Phase 1, Week 4
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        progress.report({ increment: 100, message: 'Analysis complete!' });
      });

      // For now, just show the selected code length
      vscode.window.showInformationMessage(
        `Code analysis complete! Selected ${selectedText.length} characters. (Full analysis coming in Phase 1, Week 4)`
      );

    } catch (error) {
      Logger.error('Failed to analyze code:', error);
      vscode.window.showErrorMessage('Failed to analyze code. Please try again.');
    }
  }

  /**
   * Select target folder for component generation
   */
  private async selectTargetFolder(): Promise<vscode.Uri | undefined> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      vscode.window.showErrorMessage('No workspace folder found. Please open a project folder.');
      return undefined;
    }

    // For now, just use the workspace root
    // TODO: Implement folder picker in future iterations
    return workspaceFolder.uri;
  }

  /**
   * Create new Vite React project
   */
  async createViteProject(): Promise<void> {
    try {
      Logger.info('Creating new Vite React project...');

      const projectDetectionService = this.serviceRegistry.get<ProjectDetectionService>('projectDetection');
      if (!projectDetectionService) {
        vscode.window.showErrorMessage('Project detection service not available');
        return;
      }

      // Check if project already exists
      const hasProject = await projectDetectionService.hasExistingProject();
      if (hasProject) {
        const choice = await vscode.window.showWarningMessage(
          'A project already exists in this workspace. Do you want to continue?',
          'Yes', 'No'
        );
        if (choice !== 'Yes') {
          return;
        }
      }

      // Get project name
      const projectName = await vscode.window.showInputBox({
        prompt: 'Enter project name',
        placeHolder: 'my-react-app',
        value: 'my-react-app'
      });

      if (!projectName) {
        return;
      }

      // Create project
      await projectDetectionService.createViteReactProject(projectName);

      vscode.window.showInformationMessage(
        `Vite React project "${projectName}" created successfully! Run "npm install" to install dependencies.`
      );

      Logger.info('Vite React project created successfully');

    } catch (error) {
      Logger.error('Error creating Vite project:', error);
      vscode.window.showErrorMessage(
        `Failed to create Vite project: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Analyze current workspace
   */
  async analyzeWorkspace(): Promise<void> {
    try {
      Logger.info('Analyzing workspace...');

      const workspaceAnalysisService = this.serviceRegistry.get<WorkspaceAnalysisService>('workspaceAnalysis');
      if (!workspaceAnalysisService) {
        vscode.window.showErrorMessage('Workspace analysis service not available');
        return;
      }

      const analysis = await workspaceAnalysisService.analyzeWorkspace();

      // Show analysis results
      const message = `Workspace Analysis Results:
Framework: ${analysis.framework}
Package Manager: ${analysis.packageManager}
Styling: ${analysis.styling}
Build Tool: ${analysis.buildTool}
TypeScript: ${analysis.typescript ? 'Yes' : 'No'}
Components: ${Object.keys(analysis.components).length}
Patterns: ${analysis.patterns.length}`;

      vscode.window.showInformationMessage(message);
      Logger.info('Workspace analysis completed');

    } catch (error) {
      Logger.error('Error analyzing workspace:', error);
      vscode.window.showErrorMessage(
        `Failed to analyze workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}
