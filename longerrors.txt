got this:
Code analysis complete! Selected 15 characters. (Full analysis coming in Phase 1, Week 4)

when I right click on selected code and clicked Analyze code 

when Open the UIOrbit Chat (Command Palette > "UIOrbit: Open Chat")
got: 
UIOrbit chat is now open. Start chatting with your AI frontend assistant!

Tried typing a message and pressing Enter instead of clicking Send
did not send the message just got into new like 
even clicking on ctrl+enter, and shift+enter just creating new like not sending the message 

when open Developer tool 
got these
WARN Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:3359 [Extension Host] STOPPED on first line for debugging on port 53189
F @ workbench.desktop.main.js:sourcemap:3359
workbench.desktop.main.js:sourcemap:35  INFO ComputeTargetPlatform: win32-x64
workbench.desktop.main.js:sourcemap:35  INFO Started local extension host with pid 15228.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO Loading development extension at d:\Project\New folder\uiorbit
workbench.desktop.main.js:sourcemap:35   ERR Extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<copilot-instructions.md>> to now point to mime <<text/x-instructions>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.jsx>> to now point to mime <<text/javascript>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.hbs>> to now point to mime <<text/x-handlebars>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.handlebars>> to now point to mime <<text/x-handlebars>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.jsx>> to now point to mime <<text/x-javascriptreact>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
2workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.js>> to now point to mime <<text/x-javascriptreact>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.js>> to now point to mime <<text/javascript>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
2workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.jsx>> to now point to mime <<text/javascript>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: blade
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: ejs
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: latte
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: tpl
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: twig
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: vue-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: django-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: jinja-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: erb
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: HTML (Eex)
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: volt
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: nunjucks
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: svelte
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: njk
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [sidthesloth.html5-boilerplate]: Unknown language in `contributes.html5-boilerplate.language`. Provided value: njk
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [sidthesloth.html5-boilerplate]: Unknown language in `contributes.html5-boilerplate.language`. Provided value: django-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: blade
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: ejs
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: latte
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: tpl
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: twig
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: vue-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: django-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: jinja-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: erb
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: HTML (Eex)
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: volt
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: nunjucks
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.restartLanguageServer` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.filewatcherEnable` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.filewatcherDisable` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.enableCodeLens` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.disableCodeLens` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.generate` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.refresh` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.login` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.logout` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.createProject` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.deleteProject` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.openProjectInPrismaConsole` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.createRemoteDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.createLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.getRemoteDatabaseConnectionString` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.openRemoteDatabaseInPrismaConsole` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.deleteRemoteDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.studio.launch` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.studio.launchForDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.studio.getRemoteDatabaseConnectionString` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.stopLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.startLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.copyLocalDatabaseURL` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.deleteLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.deployLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.fileWatcher'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.trace.server'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.enableCodeLens'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.enableDiagnostics'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.scriptRunner'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.schemaPath'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Cannot register multiple views with same id `prismaPostgresDatabases`
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:3602 An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.
mountTo @ workbench.desktop.main.js:sourcemap:3602
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-migrate-status': Error: Tool "prisma-migrate-status" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-migrate-dev': Error: Tool "prisma-migrate-dev" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-migrate-reset': Error: Tool "prisma-migrate-reset" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-studio': Error: Tool "prisma-studio" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-platform-login': Error: Tool "prisma-platform-login" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-postgres-create-database': Error: Tool "prisma-postgres-create-database" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  INFO Settings Sync: Account status changed from uninitialized to unavailable
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.js.jsx.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/javascript/syntaxes/JavaScriptReact.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Language.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.js.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/javascript/syntaxes/JavaScript.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Language.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.regexp.babel.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Regex.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Regex.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.css.styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mf.vscode-styled-components-0.2.2/syntaxes/css.styled.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/css.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.js.jsx.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Language.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/JavaScriptReact.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.js.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Language.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/JavaScript.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.ts.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/typescript-basics/syntaxes/TypeScript.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/TypeScript.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.tsx.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/typescript-basics/syntaxes/TypeScriptReact.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/TypeScriptReact.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.prisma.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope inline.prisma.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma-inlined.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma-inlined.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.ts.prismaClientRawSQL.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prismaClientRawSQL.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prismaClientRawSQL.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope markdown.prisma.codeblock.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma.markdown.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma.markdown.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.yaml.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/yaml/syntaxes/yaml.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/redhat.vscode-yaml-1.18.0/syntaxes/yaml.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.css.styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/css.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/styled-components.vscode-styled-components-1.7.8/syntaxes/css.styled.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mf.vscode-styled-components-0.2.2/syntaxes/styled-components.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/styled-components.vscode-styled-components-1.7.8/syntaxes/styled-components.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:35  WARN [lsadam0.ReactFlowSnippets]: One or more snippets from the extension 'ReactFlowSnippets' very likely confuse snippet-variables and snippet-placeholders (see https://code.visualstudio.com/docs/editor/userdefinedsnippets#_snippet-syntax for more details)
warn @ workbench.desktop.main.js:sourcemap:35
marketplace.visualstudio.com/_apis/public/gallery/vscode/dzannotti/vscode-babel-coloring/latest:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
marketplace.visualstudio.com/_apis/public/gallery/vscode/uiorbit/uiorbit/latest:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
workbench.desktop.main.js:sourcemap:35  INFO [perf] Render performance baseline is 47ms
workbench.desktop.main.js:sourcemap:35  INFO Auto updating outdated extensions. augment.vscode-augment
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.js.jsx.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/javascript/syntaxes/JavaScriptReact.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Language.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.js.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/javascript/syntaxes/JavaScript.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Language.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.regexp.babel.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Regex.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Regex.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.css.styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mf.vscode-styled-components-0.2.2/syntaxes/css.styled.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/css.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.js.jsx.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Language.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/JavaScriptReact.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.js.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Language.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/JavaScript.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.ts.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/typescript-basics/syntaxes/TypeScript.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/TypeScript.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.tsx.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/typescript-basics/syntaxes/TypeScriptReact.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/TypeScriptReact.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.prisma.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope inline.prisma.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma-inlined.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma-inlined.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.ts.prismaClientRawSQL.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prismaClientRawSQL.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prismaClientRawSQL.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope markdown.prisma.codeblock.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma.markdown.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma.markdown.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.yaml.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/yaml/syntaxes/yaml.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/redhat.vscode-yaml-1.18.0/syntaxes/yaml.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.css.styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/css.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/styled-components.vscode-styled-components-1.7.8/syntaxes/css.styled.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mf.vscode-styled-components-0.2.2/syntaxes/styled-components.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/styled-components.vscode-styled-components-1.7.8/syntaxes/styled-components.json
register @ TMScopeRegistry.ts:46
workbench.desktop.main.js:sourcemap:35   ERR Timed out waiting for authentication provider to register
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:35   ERR [Extension Host] (node:15228) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `Code --trace-deprecation ...` to show where the warning was created)
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1281 [Extension Host] (node:15228) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `Code --trace-deprecation ...` to show where the warning was created) (at file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720)
J1s @ workbench.desktop.main.js:sourcemap:1281
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is responsive.
workbench.desktop.main.js:sourcemap:1281 [Extension Host] Congratulations, compile hero is now active! (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
workbench.desktop.main.js:sourcemap:1281 [Extension Host] processWorkspaceFiles Array(69) true false (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
index.html:86 Uncaught SyntaxError: Failed to execute 'write' on 'Document': Invalid regular expression: missing /
    at index.html?id=da11094a-f9f4-48d4-b78d-f59189e37f84&parentId=10&origin=948c22dd-7343-4025-b236-06cca206a3d9&swVersion=4&extensionId=uiorbit.uiorbit&platform=electron&vscode-resource-base-authority=vscode-resource.vscode-cdn.net&parentOrigin=vscode-file%3A%2F%2Fvscode-app&purpose=webviewView:1062:23
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:2697 Timed out getting tasks from  typescript
(anonymous) @ workbench.desktop.main.js:sourcemap:2697
workbench.desktop.main.js:sourcemap:2697 Timed out getting tasks from  npm
(anonymous) @ workbench.desktop.main.js:sourcemap:2697
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is responsive.
workbench.desktop.main.js:sourcemap:1281 [Extension Host] [INFO] 2025-07-06T05:06:53.700Z - Translations initialized. (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
workbench.desktop.main.js:sourcemap:1281 [Extension Host] [INFO] 2025-07-06T05:06:53.708Z - Extension activated! (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
workbench.desktop.main.js:sourcemap:3602 An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.
mountTo @ workbench.desktop.main.js:sourcemap:3602
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is responsive.
check-DWGOhZOn.js:1 Failed to get pinned agents from store: Error: Request timed out: get-remote-agent-pinned-status-request, id: 2036f17e-053a-46e2-aa3b-f43a0ef5ae24
    at IconButtonAugment-CqdkuyT6.js:1:1160
getPinnedAgentsFromStore @ check-DWGOhZOn.js:1
main-panel-Bd7m3n0i.js:69 Error fetching subscription info: Error: Request timed out: get-subscription-info, id: ddd5e1f1-767c-42e5-8a9a-ffa8e7d74496
    at IconButtonAugment-CqdkuyT6.js:1:1160
fetchSubscriptionInfo @ main-panel-Bd7m3n0i.js:69
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-notification-enabled-request, id: 7e0cdb9a-8da9-4eb2-ae29-03592c85cfb6
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-status, id: 5496747f-2b13-41ca-bf95-ac10ff6c185b
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: chat-mode-changed, id: 1d169e5f-edc2-4383-beb3-5130dabb2ac9
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: agent-set-current-conversation, id: 9f60997e-e500-41d4-b3d7-253dc638f41c
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: set-current-root-task-uuid, id: 391f34d6-689d-466f-8c16-693e0f25ef7e
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: check-has-ever-used-remote-agent, id: 9898e905-2473-4580-b265-83668f417b03
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: set-has-ever-used-agent, id: 7fefdf63-323e-413b-a3df-e785bfc484bf
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: check-agent-auto-mode-approval, id: ccf904fa-8674-44cb-a8d5-c6377657da78
    at IconButtonAugment-CqdkuyT6.js:1:1160
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is responsive.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: received responsive event and cancelling profiling session
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-notification-enabled-request, id: d6209bb0-71e6-42eb-a661-14d45963b1a0
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-notification-enabled-request, id: 1c7386df-fb04-495c-8cb9-3b6229ea07ba
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: cancel-remote-agents-stream-request, id: d6c6aae2-f454-4876-aa45-6fdff14d747e
    at IconButtonAugment-CqdkuyT6.js:1:1160
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:1281 [Extension Host] ApplicationInsights:Sender Array(2) (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 15228) is responsive.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: received responsive event and cancelling profiling session
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: check-agent-auto-mode-approval, id: 9b7fbe32-8710-48ef-9948-92790d09abd3
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: agent-get-edit-list-request, id: 0ba3e408-69f4-4350-a898-7ae5357b1ce0
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-hydrated-task-request, id: 41596a57-71be-477c-91d7-e5b1f0cfb6e5
    at IconButtonAugment-CqdkuyT6.js:1:1160
folder-BB1rR2Vr.js:135 Failed to get remote url: Error: Request timed out: get-remote-url-request, id: 58f4b7f6-00b0-4acc-b153-24f59603927d
    at IconButtonAugment-CqdkuyT6.js:1:1160
getRemoteUrl @ folder-BB1rR2Vr.js:135
workbench.desktop.main.js:sourcemap:35   ERR [Extension Host] Failed to start remote agent overviews stream: Error: This operation was aborted
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:293:13968)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:518:4279)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:58439)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23027)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1281 [Extension Host] Failed to start remote agent overviews stream: Error: This operation was aborted
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:293:13968)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:518:4279)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:58439)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23027)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052 (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
main-panel-Bd7m3n0i.js:69 Error fetching subscription info: Error: Request timed out: get-subscription-info, id: 15fbc630-a4cd-4383-b068-8aeaf01120b5
    at IconButtonAugment-CqdkuyT6.js:1:1160
fetchSubscriptionInfo @ main-panel-Bd7m3n0i.js:69
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-notification-enabled-request, id: dab3fc5e-af5a-4e9c-9efc-a7c05e36269c
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: agent-get-edit-list-request, id: 3be63866-63a2-42e7-a08e-21dc85cbc3be
    at IconButtonAugment-CqdkuyT6.js:1:1160
workbench.desktop.main.js:sourcemap:35   ERR [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at processTimers (node:internal/timers:520:9)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1281 [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at processTimers (node:internal/timers:520:9)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052 (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
diff-utils-RpWUB_Gw.js:45 Retrying remote agent stream in 0 seconds... (Attempt 1 of 5)
console.warn @ diff-utils-RpWUB_Gw.js:45
workbench.desktop.main.js:sourcemap:35   ERR [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at processTimers (node:internal/timers:520:9)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1281 [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at processTimers (node:internal/timers:520:9)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052 (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
diff-utils-RpWUB_Gw.js:45 Retrying remote agent stream in 0 seconds... (Attempt 1 of 5)
console.warn @ diff-utils-RpWUB_Gw.js:45
workbench.desktop.main.js:sourcemap:1281 [Extension Host] [FileSystemService] vscode-userdata:/c%3A/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/commandEmbeddings.json is a LARGE file (15MB > 5MB) (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
workbench.desktop.main.js:sourcemap:35   ERR [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1281 [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052 (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
diff-utils-RpWUB_Gw.js:45 Retrying remote agent stream in 0 seconds... (Attempt 1 of 5)
console.warn @ diff-utils-RpWUB_Gw.js:45



and in debug console 
(node:15228) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `Code --trace-deprecation ...` to show where the warning was created)
extensionHostProcess.js:177
Congratulations, compile hero is now active!
extensionHostProcess.js:177
processWorkspaceFiles (69) [Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa] true false
extensionHostProcess.js:177
[INFO] 2025-07-06T05:06:53.700Z - Translations initialized.
extensionHostProcess.js:177
[INFO] 2025-07-06T05:06:53.708Z - Extension activated!
extensionHostProcess.js:177
ApplicationInsights:Sender (2) ['Ingestion endpoint could not be reached. This bat… to enable resending of failed telemetry. Error:', Array(1)]
extensionHostProcess.js:177
Failed to start remote agent overviews stream: e: This operation was aborted
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:293:13968)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:518:4279)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:58439)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23027)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052 {status: 3, errorDetails: undefined, stack: 'Error: This operation was aborted
    at e.tr…e-augment-0.487.1\\out\\extension.js:527:5052', message: 'This operation was aborted', cause: DOMException}

extensionHostProcess.js:177
Failed to start remote agent overviews stream: DOMException {stack: 'AbortError: STREAM_TIMEOUT
    at new DOMExce…e-augment-0.487.1\\out\\extension.js:527:5052'}
extensionHostProcess.js:177
Failed to start remote agent overviews stream: DOMException {stack: 'AbortError: STREAM_TIMEOUT
    at new DOMExce…e-augment-0.487.1\\out\\extension.js:527:5052'}
extensionHostProcess.js:177
[FileSystemService] vscode-userdata:/c%3A/Users/<USER>/AppData/Roaming/Code/User/globalStorage/github.copilot-chat/commandEmbeddings.json is a LARGE file (15MB > 5MB)
extensionHostProcess.js:177
Failed to start remote agent overviews stream: DOMException {stack: 'AbortError: STREAM_TIMEOUT
    at new DOMExce…e-augment-0.487.1\\out\\extension.js:527:5052'}
extensionHostProcess.js:177
Failed to start remote agent overviews stream: DOMException {stack: 'AbortError: STREAM_TIMEOUT
    at new DOMExce…e-augment-0.487.1\\out\\extension.js:527:5052'}