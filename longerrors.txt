from debug console of vs code 
(node:9336) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `Code --trace-deprecation ...` to show where the warning was created)
extensionHostProcess.js:177
Congratulations, compile hero is now active!
extensionHostProcess.js:177
processWorkspaceFiles (69) [Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa, Sa] true false
extensionHostProcess.js:177
[INFO] 2025-07-06T05:40:10.122Z - Translations initialized.
extensionHostProcess.js:177
[INFO] 2025-07-06T05:40:10.136Z - Extension activated!
extensionHostProcess.js:177
Failed to start remote agent overviews stream: e: This operation was aborted
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:293:13968)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:518:4279)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:58439)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23027)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052 {status: 3, errorDetails: undefined, stack: 'Error: This operation was aborted
    at e.tr…e-augment-0.487.1\\out\\extension.js:527:5052', message: 'This operation was aborted', cause: DOMException}

extensionHostProcess.js:177
rejected promise not handled within 1 second: Error: fetch failed
extensionHostProcess.js:177
stack trace: Error: fetch failed
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:293:13968)
    at wM.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:12446)
    at wM.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:57610)
    at wM.uploadUserEvents (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:52032)
    at poe.uploadUserEvents (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1864:10975)
extensionHostProcess.js:177
Failed to start remote agent overviews stream: DOMException {stack: 'AbortError: STREAM_TIMEOUT
    at new DOMExce…e-augment-0.487.1\\out\\extension.js:527:5052'}

from Developer Tool
 WARN Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:3359 [Extension Host] STOPPED on first line for debugging on port 53985
F @ workbench.desktop.main.js:sourcemap:3359
workbench.desktop.main.js:sourcemap:35  INFO ComputeTargetPlatform: win32-x64
workbench.desktop.main.js:sourcemap:35  INFO Started local extension host with pid 9336.
addon-webgl.js:1 task queue exceeded allotted deadline by 236ms
_process @ addon-webgl.js:1
addon-webgl.js:1 task queue exceeded allotted deadline by 73ms
_process @ addon-webgl.js:1
addon-webgl.js:1 task queue exceeded allotted deadline by 222ms
_process @ addon-webgl.js:1
workbench.desktop.main.js:sourcemap:35  INFO Loading development extension at d:\Project\New folder\uiorbit
workbench.desktop.main.js:sourcemap:35   ERR Extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<copilot-instructions.md>> to now point to mime <<text/x-instructions>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.jsx>> to now point to mime <<text/javascript>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.hbs>> to now point to mime <<text/x-handlebars>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.handlebars>> to now point to mime <<text/x-handlebars>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.jsx>> to now point to mime <<text/x-javascriptreact>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
2workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.js>> to now point to mime <<text/x-javascriptreact>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.js>> to now point to mime <<text/javascript>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
2workbench.desktop.main.js:sourcemap:685 Overwriting extension <<.jsx>> to now point to mime <<text/javascript>>
(anonymous) @ workbench.desktop.main.js:sourcemap:685
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: blade
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: ejs
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: latte
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: tpl
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: twig
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: vue-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: django-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: jinja-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: erb
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: HTML (Eex)
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: volt
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: nunjucks
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: svelte
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [AnbuselvanRocky.bootstrap5-vscode]: Unknown language in `contributes.bootstrap5-vscode.language`. Provided value: njk
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [sidthesloth.html5-boilerplate]: Unknown language in `contributes.html5-boilerplate.language`. Provided value: njk
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [sidthesloth.html5-boilerplate]: Unknown language in `contributes.html5-boilerplate.language`. Provided value: django-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: blade
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: ejs
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: latte
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: tpl
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: twig
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: vue-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: django-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: jinja-html
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: erb
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: HTML (Eex)
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: volt
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [thekalinga.bootstrap4-vscode]: Unknown language in `contributes.bootstrap4-vscode.language`. Provided value: nunjucks
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.restartLanguageServer` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.filewatcherEnable` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.filewatcherDisable` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.enableCodeLens` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.disableCodeLens` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.generate` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.refresh` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.login` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.logout` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.createProject` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.deleteProject` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.openProjectInPrismaConsole` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.createRemoteDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.createLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.getRemoteDatabaseConnectionString` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.openRemoteDatabaseInPrismaConsole` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.deleteRemoteDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.studio.launch` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.studio.launchForDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.studio.getRemoteDatabaseConnectionString` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.stopLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.startLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.copyLocalDatabaseURL` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.deleteLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  INFO [Prisma.prisma-insider]: Command `prisma.deployLocalDatabase` already registered by Prisma (Prisma.prisma)
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.fileWatcher'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.trace.server'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.enableCodeLens'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.enableDiagnostics'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.scriptRunner'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN [Prisma.prisma-insider]: Cannot register 'prisma.schemaPath'. This property is already registered.
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Cannot register multiple views with same id `prismaPostgresDatabases`
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:3602 An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.
mountTo @ workbench.desktop.main.js:sourcemap:3602
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-migrate-status': Error: Tool "prisma-migrate-status" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-migrate-dev': Error: Tool "prisma-migrate-dev" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-migrate-reset': Error: Tool "prisma-migrate-reset" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-studio': Error: Tool "prisma-studio" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-platform-login': Error: Tool "prisma-platform-login" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Prisma.prisma-insider]: Failed to register tool 'prisma-postgres-create-database': Error: Tool "prisma-postgres-create-database" is already registered.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.js.jsx.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/javascript/syntaxes/JavaScriptReact.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Language.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.js.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/javascript/syntaxes/JavaScript.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Language.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.regexp.babel.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Regex.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Regex.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.css.styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mf.vscode-styled-components-0.2.2/syntaxes/css.styled.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/css.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.js.jsx.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Language.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/JavaScriptReact.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.js.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Language.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/JavaScript.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.ts.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/typescript-basics/syntaxes/TypeScript.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/TypeScript.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.tsx.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/typescript-basics/syntaxes/TypeScriptReact.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/TypeScriptReact.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.prisma.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope inline.prisma.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma-inlined.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma-inlined.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.ts.prismaClientRawSQL.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prismaClientRawSQL.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prismaClientRawSQL.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope markdown.prisma.codeblock.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma.markdown.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma.markdown.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.yaml.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/yaml/syntaxes/yaml.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/redhat.vscode-yaml-1.18.0/syntaxes/yaml.tmLanguage.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope source.css.styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/css.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/styled-components.vscode-styled-components-1.7.8/syntaxes/css.styled.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:1872 Overwriting grammar scope name to file mapping for scope styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mf.vscode-styled-components-0.2.2/syntaxes/styled-components.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/styled-components.vscode-styled-components-1.7.8/syntaxes/styled-components.json
register @ workbench.desktop.main.js:sourcemap:1872
workbench.desktop.main.js:sourcemap:35  WARN [lsadam0.ReactFlowSnippets]: One or more snippets from the extension 'ReactFlowSnippets' very likely confuse snippet-variables and snippet-placeholders (see https://code.visualstudio.com/docs/editor/userdefinedsnippets#_snippet-syntax for more details)
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  INFO Settings Sync: Account status changed from uninitialized to unavailable
workbench.desktop.main.js:sourcemap:1872 Time limit reached when tokenizing line: import NotFound from "./pages/NotFound";
tokenizeEncoded @ workbench.desktop.main.js:sourcemap:1872
marketplace.visualstudio.com/_apis/public/gallery/vscode/dzannotti/vscode-babel-coloring/latest:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
marketplace.visualstudio.com/_apis/public/gallery/vscode/uiorbit/uiorbit/latest:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is unresponsive.
workbench.desktop.main.js:sourcemap:35  INFO [perf] Render performance baseline is 83ms
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.js.jsx.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/javascript/syntaxes/JavaScriptReact.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Language.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.js.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/javascript/syntaxes/JavaScript.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Language.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.regexp.babel.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Regex.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Regex.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.css.styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mf.vscode-styled-components-0.2.2/syntaxes/css.styled.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/css.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.js.jsx.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/dzannotti.vscode-babel-coloring-0.0.4/syntaxes/Babel%20Language.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/JavaScriptReact.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.js.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/Babel-Language.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/JavaScript.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.ts.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/typescript-basics/syntaxes/TypeScript.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/TypeScript.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.tsx.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/typescript-basics/syntaxes/TypeScriptReact.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/ms-vscode.vscode-typescript-next-5.9.20250705/syntaxes/TypeScriptReact.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.prisma.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope inline.prisma.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma-inlined.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma-inlined.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.ts.prismaClientRawSQL.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prismaClientRawSQL.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prismaClientRawSQL.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope markdown.prisma.codeblock.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-6.11.1/syntaxes/prisma.markdown.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/prisma.prisma-insider-31.0.7731/syntaxes/prisma.markdown.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.yaml.
Old grammar file: file:///c%3A/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/yaml/syntaxes/yaml.tmLanguage.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/redhat.vscode-yaml-1.18.0/syntaxes/yaml.tmLanguage.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope source.css.styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mgmcdermott.vscode-language-babel-0.0.40/grammars/css.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/styled-components.vscode-styled-components-1.7.8/syntaxes/css.styled.json
register @ TMScopeRegistry.ts:46
TMScopeRegistry.ts:46 Overwriting grammar scope name to file mapping for scope styled.
Old grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/mf.vscode-styled-components-0.2.2/syntaxes/styled-components.json.
New grammar file: file:///c%3A/Users/<USER>/.vscode/extensions/styled-components.vscode-styled-components-1.7.8/syntaxes/styled-components.json
register @ TMScopeRegistry.ts:46
textMateTokenizationSupport.ts:63 Time limit reached when tokenizing line: const queryClient = new QueryClient();
tokenizeEncoded @ textMateTokenizationSupport.ts:63
workbench.desktop.main.js:sourcemap:35  INFO Auto updating outdated extensions. augment.vscode-augment
workbench.desktop.main.js:sourcemap:35   ERR [Extension Host] (node:9336) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `Code --trace-deprecation ...` to show where the warning was created)
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1281 [Extension Host] (node:9336) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `Code --trace-deprecation ...` to show where the warning was created) (at file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720)
J1s @ workbench.desktop.main.js:sourcemap:1281
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is responsive.
workbench.desktop.main.js:sourcemap:1281 [Extension Host] Congratulations, compile hero is now active! (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
workbench.desktop.main.js:sourcemap:1281 [Extension Host] processWorkspaceFiles Array(69) true false (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:2697 Timed out getting tasks from  typescript
(anonymous) @ workbench.desktop.main.js:sourcemap:2697
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is responsive.
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is responsive.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: received responsive event and cancelling profiling session
workbench.desktop.main.js:sourcemap:1281 [Extension Host] [INFO] 2025-07-06T05:40:10.122Z - Translations initialized. (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
workbench.desktop.main.js:sourcemap:1281 [Extension Host] [INFO] 2025-07-06T05:40:10.136Z - Extension activated! (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
workbench.desktop.main.js:sourcemap:3602 An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.
mountTo @ workbench.desktop.main.js:sourcemap:3602
index.html:101 Uncaught SyntaxError: Failed to execute 'write' on 'Document': Invalid regular expression: missing /
    at index.html?id=f288f3ef-8557-4425-bbbe-0fb264e5c7d7&parentId=11&origin=948c22dd-7343-4025-b236-06cca206a3d9&swVersion=4&extensionId=uiorbit.uiorbit&platform=electron&vscode-resource-base-authority=vscode-resource.vscode-cdn.net&parentOrigin=vscode-file%3A%2F%2Fvscode-app&purpose=webviewView:1062:23
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is responsive.
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is responsive.
check-DWGOhZOn.js:1 Failed to get pinned agents from store: Error: Request timed out: get-remote-agent-pinned-status-request, id: 8a826226-5ac2-4bc0-b43e-7e8a2105887a
    at IconButtonAugment-CqdkuyT6.js:1:1160
getPinnedAgentsFromStore @ check-DWGOhZOn.js:1
main-panel-Bd7m3n0i.js:69 Error fetching subscription info: Error: Request timed out: get-subscription-info, id: 0830392c-40c7-4fd3-a57e-3daeee78a6c4
    at IconButtonAugment-CqdkuyT6.js:1:1160
fetchSubscriptionInfo @ main-panel-Bd7m3n0i.js:69
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-notification-enabled-request, id: 08d59d1f-d14e-4bd2-a5a5-4135d82ec4b7
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-status, id: 38894768-aa07-400f-9ce1-4e643861edce
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: chat-mode-changed, id: 490bef03-95e4-44bf-aedb-fad0bcdbed79
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: agent-set-current-conversation, id: ad9294c2-6893-445f-82c6-0b55788ff126
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: set-current-root-task-uuid, id: f6281a6e-93e5-477a-8e02-7e354ca2d39f
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: check-has-ever-used-remote-agent, id: 1c02e412-eb07-4426-9030-1134ddeb59e4
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: set-has-ever-used-agent, id: f63b80ec-e355-4728-89e7-61c90a957cfb
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: check-agent-auto-mode-approval, id: b92f1f80-766f-4e00-a232-b5a0d5526e23
    at IconButtonAugment-CqdkuyT6.js:1:1160
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is unresponsive.
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/RemoteAgentRetry-DYgtEKQz.css:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/CopyButton-C581jDHd.css:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/LanguageIcon-D78BqCXT.css:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/IconFilePath-CiKel2Kp.css:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/AugmentMessage-C9VpHwlQ.css:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/MessageList-DRTeF5X0.css:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/next-edit-types-904A5ehg.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/RemoteAgentRetry-Bi74U8IF.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/CopyButton-OCXKxiUh.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/chevron-down-CxktpQeS.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/mcp-logo-CMUqUebQ.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/expand-CRxF6TiY.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/magnifying-glass-4Ft8m82l.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/IconFilePath-BhE4l2UK.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/MessageList-C4JGFtcq.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/AugmentMessage-B1RETs6x.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.487.1/common-webviews/assets/LanguageIcon-CARIV-0P.js:1 
            
            
           Failed to load resource: the server responded with a status of 408 ()
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: agent-get-edit-list-request, id: 6a78a9e2-ca44-4974-a5c8-a9a5fabcc095
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-hydrated-task-request, id: 58094e9e-c574-4f37-ba0b-961f667cf630
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-notification-enabled-request, id: 3c7c71fd-f4f9-47d0-9cde-1378a2ac97ec
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-notification-enabled-request, id: 0b491dd0-c216-4109-be63-b26e514581e5
    at IconButtonAugment-CqdkuyT6.js:1:1160
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: cancel-remote-agents-stream-request, id: 88c30356-d920-4639-821b-0c1fe9797669
    at IconButtonAugment-CqdkuyT6.js:1:1160
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is responsive.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: received responsive event and cancelling profiling session
workbench.desktop.main.js:sourcemap:35  WARN UNRESPONSIVE extension host: 'augment.vscode-augment' took 34.06452159665527% of 24.117ms, saved PROFILE here: 'file:///c%3A/Users/<USER>/AppData/Local/Temp/exthost-acbfe2.cpuprofile'
warn @ workbench.desktop.main.js:sourcemap:35
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: check-agent-auto-mode-approval, id: fd1671e0-4604-483c-863e-b3714293f5df
    at IconButtonAugment-CqdkuyT6.js:1:1160
main-panel-Bd7m3n0i.js:69 Error fetching subscription info: Error: Request timed out: get-subscription-info, id: b769459a-b047-494f-b6c9-d58ae74b342a
    at IconButtonAugment-CqdkuyT6.js:1:1160
fetchSubscriptionInfo @ main-panel-Bd7m3n0i.js:69
folder-BB1rR2Vr.js:135 Failed to get remote url: Error: Request timed out: get-remote-url-request, id: 78553027-0eb4-489b-b103-5180a926e5cc
    at IconButtonAugment-CqdkuyT6.js:1:1160
getRemoteUrl @ folder-BB1rR2Vr.js:135
workbench.desktop.main.js:sourcemap:35   ERR [Extension Host] Failed to start remote agent overviews stream: Error: This operation was aborted
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:293:13968)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:518:4279)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:58439)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23027)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1281 [Extension Host] Failed to start remote agent overviews stream: Error: This operation was aborted
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:293:13968)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:518:4279)
    at wM.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:58439)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23027)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052 (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is unresponsive.
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
main-panel-Bd7m3n0i.js:69 Error fetching subscription info: Error: Request timed out: get-subscription-info, id: 8ae8490a-8084-4164-b213-8b6300f9dd77
    at IconButtonAugment-CqdkuyT6.js:1:1160
fetchSubscriptionInfo @ main-panel-Bd7m3n0i.js:69
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is responsive.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: received responsive event and cancelling profiling session
IconButtonAugment-CqdkuyT6.js:1 Uncaught (in promise) Error: Request timed out: get-remote-agent-notification-enabled-request, id: d45ffa93-bb1e-4ef7-a67d-eab8187fba92
    at IconButtonAugment-CqdkuyT6.js:1:1160
workbench.desktop.main.js:sourcemap:1281 [Extension Host] rejected promise not handled within 1 second: Error: fetch failed (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
workbench.desktop.main.js:sourcemap:1281 [Extension Host] stack trace: Error: fetch failed
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:293:13968)
    at wM.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:12446)
    at wM.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:57610)
    at wM.uploadUserEvents (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:52032)
    at poe.uploadUserEvents (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1864:10975) (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
workbench.desktop.main.js:sourcemap:1299 [Augment.vscode-augment]fetch failed
$onExtensionRuntimeError @ workbench.desktop.main.js:sourcemap:1299
workbench.desktop.main.js:sourcemap:1299 Error: fetch failed
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:293:13968)
    at wM.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:12446)
    at wM.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:57610)
    at wM.uploadUserEvents (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:52032)
    at poe.uploadUserEvents (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1864:10975)
$onExtensionRuntimeError @ workbench.desktop.main.js:sourcemap:1299
workbench.desktop.main.js:sourcemap:35   ERR An unknown error occurred. Please consult the log for more details.
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  WARN UNRESPONSIVE extension host: 'vscode.markdown-language-features' took 40.251098663631346% of 94.064ms, saved PROFILE here: 'file:///c%3A/Users/<USER>/AppData/Local/Temp/exthost-159372.cpuprofile'
warn @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is unresponsive.
workbench.desktop.main.js:sourcemap:35  INFO Extension host (LocalProcess pid: 9336) is responsive.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: received responsive event and cancelling profiling session
workbench.desktop.main.js:sourcemap:3359 Extension Host
workbench.desktop.main.js:sourcemap:3359 Debugger attached.
workbench.desktop.main.js:sourcemap:35  INFO UNRESPONSIVE extension host: starting to profile NOW
workbench.desktop.main.js:sourcemap:35   ERR [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1281 [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052 (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
check-DWGOhZOn.js:1 Retrying remote agent stream in 0 seconds... (Attempt 1 of 5)
getStream @ check-DWGOhZOn.js:1
workbench.desktop.main.js:sourcemap:35   ERR TreeError [DebugRepl] Tree input not set: Error: TreeError [DebugRepl] Tree input not set
    at Zg.C (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:417:29025)
    at Zg.updateChildren (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:417:28932)
    at Jre.value (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:2439:33732)
    at E.B (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:29:2392)
    at E.C (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:29:2462)
    at E.fire (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:29:2680)
    at Gke.setVisible (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:680:2729)
    at vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1315:11772
    at Array.map (<anonymous>)
    at td.setVisible (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1315:11763)
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:35   ERR [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052
error @ workbench.desktop.main.js:sourcemap:35
workbench.desktop.main.js:sourcemap:1281 [Extension Host] Failed to start remote agent overviews stream: AbortError: STREAM_TIMEOUT
    at new DOMException (node:internal/per_context/domexception:53:5)
    at A (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:520:325)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at wM.getRemoteAgentOverviewsStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:522:23129)
    at e.handleRemoteAgentOverviewsStreamRequest (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:1771:21995)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.487.1\out\extension.js:527:5052 (at console.<anonymous> (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:28720))
J1s @ workbench.desktop.main.js:sourcemap:1281
check-DWGOhZOn.js:1 Retrying remote agent stream in 0 seconds... (Attempt 1 of 5)
getStream @ check-DWGOhZOn.js:1

and when I select some code and right click and click on Analyze code 
it is showing Code analysis complete! Selected 350 characters. (Full analysis coming in Phase 1, Week 4)

and Try sending a message by:
Typing and clicking "Send"
Typing and pressing "Enter"
Both Not working 